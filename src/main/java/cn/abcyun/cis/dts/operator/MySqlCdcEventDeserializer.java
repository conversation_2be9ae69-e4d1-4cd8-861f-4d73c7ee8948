package cn.abcyun.cis.dts.operator;

import cn.abcyun.cis.dts.model.ChangeEvent;
import cn.abcyun.commons.flink.common.utils.JsonUtils;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.source.SourceRecord;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Struct;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Field;
import com.ververica.cdc.debezium.DebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.TypeExtractor;
import org.apache.flink.util.Collector;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class MySqlCdcEventDeserializer implements DebeziumDeserializationSchema<ChangeEvent>, Serializable {
    private static final long serialVersionUID = 1L;

    @Override
    public void deserialize(SourceRecord record, Collector<ChangeEvent> collector) {
        try {
            log.warn("fetch record: {}", record);
            // Get value
            Struct value = (Struct) record.value();
            if (value == null) {
                log.warn("Record value is null, skipping: {}", record);
                return;
            }
            // Get operation type
            String op = null;
            if (value.schema().field("op") != null) {
                op = value.getString("op");
            }

            if (op == null || "d".equals(op)) {
                return;
            }

            Long ts = null;
            String gtid = null;
            if (value.schema().field("source") != null) {
                Struct source = value.getStruct("source");
                if (source != null) {
                    ts = source.getInt64("ts_ms");
                    gtid = source.getString("gtid");
                }
            }

            // Extract database and table name from topic
            String topic = record.topic();
            String[] topicParts = topic.split("\\.");
            String database = topicParts.length > 1 ? topicParts[1] : "";
            String table = topicParts.length > 2 ? topicParts[2] : "";

            // Create change event
            ChangeEvent changeEvent = new ChangeEvent();
            changeEvent.setDatabase(database);
            changeEvent.setTable(table);
            changeEvent.setTimestamp(ts);
            changeEvent.setGtid(gtid);

            // 提取并设置 Key
            if (record.key() != null && record.key() instanceof Struct) {
                Struct keyStruct = (Struct) record.key();
                changeEvent.setKey(convertStruct(keyStruct));
                log.debug("Extracted key: {}", changeEvent.getKey());
            }

            // Process data change event
            Struct before = null;
            Struct after = null;

            if (value.schema().field("before") != null) {
                before = value.getStruct("before");
            }

            if (value.schema().field("after") != null) {
                after = value.getStruct("after");
            }

            // Set event type and data based on operation
            switch (op) {
                case "c": // Create
                case "r": // Read
                    changeEvent.setEventType(ChangeEvent.EventType.INSERT);
                    if (after != null) {
                        changeEvent.setAfter(convertStruct(after));
                    }
                    break;
                case "u": // Update
                    changeEvent.setEventType(ChangeEvent.EventType.UPDATE);
                    if (before != null) {
                        changeEvent.setBefore(convertStruct(before));
                    }
                    if (after != null) {
                        changeEvent.setAfter(convertStruct(after));
                    }
                    break;
                case "d": // Delete
                    changeEvent.setEventType(ChangeEvent.EventType.DELETE);
                    if (before != null) {
                        changeEvent.setBefore(convertStruct(before));
                    }
                    break;
                default:
                    return;
            }
            log.warn("fetch changeEvent: {}", JsonUtils.dump(changeEvent));
            // Collect event
            collector.collect(changeEvent);
        } catch (Exception e) {
            log.error("Error deserializing CDC event: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * Convert Debezium Struct to Map
     */
    private Map<String, Object> convertStruct(Struct struct) {
        if (struct == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        log.warn(struct.schema().fields().toString());
        for (Field field : struct.schema().fields()) {
            String fieldName = field.name();
            Object fieldValue = struct.get(field);
            result.put(fieldName, fieldValue);
        }
        return result;
    }

    @Override
    public TypeInformation<ChangeEvent> getProducedType() {
        return TypeExtractor.getForClass(ChangeEvent.class);
    }
}