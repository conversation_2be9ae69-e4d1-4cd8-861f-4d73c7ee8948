package cn.abcyun.cis.dts.operator;

import cn.abcyun.cis.dts.model.ChangeEvent;
import cn.abcyun.cis.dts.model.EventType;
import org.apache.flink.api.common.functions.FilterFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DeleteEventFilter implements FilterFunction<ChangeEvent> {
    private static final Logger LOG = LoggerFactory.getLogger(DeleteEventFilter.class);
    
    @Override
    public boolean filter(ChangeEvent event) {
        boolean isNotDelete = event.getEventType() != EventType.DELETE;
        
        if (!isNotDelete) {
            LOG.info("Filtering out DELETE event for table: {}.{}", 
                    event.getDatabase(), event.getTable());
        }
        
        return isNotDelete;
    }
}
