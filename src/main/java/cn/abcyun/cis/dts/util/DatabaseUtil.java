package cn.abcyun.cis.dts.util;

import cn.abcyun.cis.dts.config.MySQLConfig;
import cn.abcyun.cis.dts.model.ChangeEvent;
import org.apache.commons.dbcp2.BasicDataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.NonTransientDataAccessException;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

public class DatabaseUtil {
    private static final Logger LOG = LoggerFactory.getLogger(DatabaseUtil.class);

    // 使用 ThreadLocal 来存储每个线程的 JdbcTemplate
    private static final ThreadLocal<NamedParameterJdbcTemplate> jdbcTemplateThreadLocal = new ThreadLocal<>();

    /**
     * 获取当前线程的数据源
     */
    private static BasicDataSource getDataSource() {
        NamedParameterJdbcTemplate jdbcTemplate = jdbcTemplateThreadLocal.get();
        if (jdbcTemplate == null) {
            return null;
        }
        return (BasicDataSource) jdbcTemplate.getJdbcTemplate().getDataSource();
    }

    public static void initConnectionPool(MySQLConfig config) {
        // 检查当前线程的数据源是否已关闭
        BasicDataSource currentDataSource = getDataSource();
        if (currentDataSource != null && currentDataSource.isClosed()) {
            LOG.info("Removing closed JDBC template from thread local");
            jdbcTemplateThreadLocal.remove();
            currentDataSource = null;
        }

        // 如果当前线程没有数据源，创建一个新的
        if (currentDataSource == null) {
            LOG.info("Creating new data source for thread: {}", Thread.currentThread().getName());
            BasicDataSource newDataSource = new BasicDataSource();
            newDataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
            String jdbcUrl = String.format("************************************",
                    config.getHost(), config.getPort(), config.getServerTimeZone());
            newDataSource.setUrl(jdbcUrl);
            newDataSource.setUsername(config.getUsername());
            newDataSource.setPassword(config.getPassword());
            newDataSource.setInitialSize(config.getConnectionPoolSize());
            newDataSource.setMaxTotal(config.getConnectionPoolSize());

            // 设置连接验证
            newDataSource.setTestOnBorrow(true);
            newDataSource.setValidationQuery("SELECT 1");

            // 创建 NamedParameterJdbcTemplate
            NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(newDataSource);
            jdbcTemplateThreadLocal.set(jdbcTemplate);

            LOG.info("Database connection pool and JDBC template initialized with URL: {}", jdbcUrl);
        }
    }

    public static void executeEvent(ChangeEvent event) throws SQLException {
        NamedParameterJdbcTemplate jdbcTemplate = jdbcTemplateThreadLocal.get();
        BasicDataSource dataSource = getDataSource();

        if (jdbcTemplate == null || dataSource == null || dataSource.isClosed()) {
            throw new SQLException("JDBC template not initialized or data source closed");
        }

        try {
            Map<String, Object> paramMap;
            String sql;

            switch (event.getEventType()) {
                case INSERT:
                    sql = generateNamedInsertSql(event);
                    paramMap = event.getAfter();
                    break;
                case UPDATE:
                    // 使用 Key 作为更新条件
                    Map<String, Object> keyCondition = event.getPrimaryKeyCondition();
                    if (keyCondition == null || keyCondition.isEmpty()) {
                        LOG.warn("No primary key found for UPDATE event, using before data as condition: {}", event);
                        keyCondition = event.getBefore();
                    }

                    sql = generateNamedUpdateSql(event);
                    paramMap = prepareUpdateParameters(event.getAfter(), keyCondition);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported event type: " + event.getEventType());
            }

            LOG.debug("Executing SQL: {}", sql);
            int rowsAffected = jdbcTemplate.update(sql, paramMap);
            LOG.debug("SQL executed successfully, rows affected: {}", rowsAffected);
        } catch (NonTransientDataAccessException e) {
            LOG.error("skip Non-transient error executing SQL: {}", event, e);
        } catch (Exception e) {
            LOG.error("Error executing SQL for event: {}", event, e);
            throw new SQLException("Error executing SQL", e);
        }
    }

    /**
     * 生成带命名参数的 INSERT SQL
     */
    private static String generateNamedInsertSql(ChangeEvent event) {
        Map<String, Object> data = event.getAfter();
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("No data for INSERT event");
        }

        String columns = data.keySet().stream()
                .map(column -> "`" + column + "`")
                .collect(Collectors.joining(", "));

        String placeholders = data.keySet().stream()
                .map(k -> ":" + k)
                .collect(Collectors.joining(", "));

        return String.format("INSERT INTO `%s.%s` (%s) VALUES (%s)", generateRecordDbName(event.getDatabase()), event.getTable(), columns, placeholders);
    }

    private static String generateRecordDbName(String dbName) {
        if (StringUtils.isBlank(dbName)) {
            return null;
        }
        String recordDbName = null;
        if (dbName.endsWith("_dev")) {
            recordDbName = dbName.replace("_dev", "_record_dev");
        } else if (dbName.endsWith("_test")) {
            recordDbName = dbName.replace("_test", "_record_test");
        } else {
            recordDbName = dbName + "_record";
        }
        return recordDbName;
    }

    /**
     * 生成带命名参数的 UPDATE SQL
     * 使用 Key 作为更新条件，而不是使用所有 before 字段
     */
    private static String generateNamedUpdateSql(ChangeEvent event) {
        Map<String, Object> data = event.getAfter();
        Map<String, Object> keyCondition = event.getPrimaryKeyCondition();

        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("No data for UPDATE event");
        }

        if (keyCondition == null || keyCondition.isEmpty()) {
            throw new IllegalArgumentException("No key condition for UPDATE event");
        }

        StringJoiner setClause = new StringJoiner(", ");
        for (String column : data.keySet()) {
            // 跳过主键字段，不在 SET 子句中更新主键
            if (!keyCondition.containsKey(column)) {
                setClause.add("`" + column + "` = :set_" + column);
            }
        }

        StringJoiner whereClause = new StringJoiner(" AND ");
        for (String column : keyCondition.keySet()) {
            whereClause.add("`" + column + "` = :key_" + column);
        }

        return String.format("UPDATE `%s.%s` SET %s WHERE %s", generateRecordDbName(event.getDatabase()), event.getTable(), setClause, whereClause);
    }

    /**
     * 准备 INSERT 语句的参数映射
     */
    private static Map<String, Object> prepareInsertParameters(Map<String, Object> data) {
        Map<String, Object> params = new HashMap<>();

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();
            params.put(key, entry.getValue());
        }

        return params;
    }

    /**
     * 准备 UPDATE 语句的参数映射
     * 使用 Key 作为更新条件
     */
    private static Map<String, Object> prepareUpdateParameters(Map<String, Object> data, Map<String, Object> condition) {
        Map<String, Object> params = new HashMap<>();

        // 获取主键条件
        Map<String, Object> keyCondition = new HashMap<>();
        if (condition != null) {
            // 假设 id 是主键
            if (condition.containsKey("id")) {
                keyCondition.put("id", condition.get("id"));
            }
        }

        // 添加 SET 子句的参数
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            // 跳过主键字段
            if (!keyCondition.containsKey(entry.getKey())) {
                String key = "set_" + entry.getKey();
                params.put(key, entry.getValue());
            }
        }

        // 添加 WHERE 子句的参数（主键条件）
        for (Map.Entry<String, Object> entry : keyCondition.entrySet()) {
            String key = "key_" + entry.getKey();
            params.put(key, entry.getValue());
        }

        return params;
    }


    public static void closeConnectionPool() {
        BasicDataSource dataSource = getDataSource();
        if (dataSource != null && !dataSource.isClosed()) {
            try {
                dataSource.close();
                jdbcTemplateThreadLocal.remove();
                LOG.info("JDBC template and database connection pool closed for thread: {}", Thread.currentThread().getName());
            } catch (SQLException e) {
                LOG.error("Error closing database connection pool", e);
            }
        }
    }
}