package cn.abcyun.cis.dts;

import cn.abcyun.cis.dts.config.AppConfig;
import cn.abcyun.cis.dts.config.MySQLConfig;
import cn.abcyun.cis.dts.operator.CombineWindowFunction;
import cn.abcyun.cis.dts.operator.GtidAwareWatermarkGenerator;
import cn.abcyun.cis.dts.operator.MySQLSinkFunction;
import cn.abcyun.cis.dts.operator.PrimaryKeySelector;
import cn.abcyun.cis.dts.util.CommonUtils;
import cn.abcyun.cis.dts.util.Constant;
import cn.abcyun.cis.dts.util.PropertiesLoader;
import cn.abcyun.cis.dts.model.ChangeEvent;
import cn.abcyun.cis.dts.operator.MySqlCdcEventDeserializer;
import cn.abcyun.commons.flink.common.*;
import cn.abcyun.commons.flink.common.utils.AbcGson;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Schema;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.SchemaBuilder;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Struct;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.json.DecimalFormat;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.json.JsonConverterConfig;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import com.ververica.cdc.debezium.StringDebeziumDeserializationSchema;
import io.debezium.data.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.runtime.state.KeyGroupRangeAssignment;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.windows.GlobalWindow;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class Hot2ColdBusinessSyncJob {
    public static void main(String[] args) throws Exception {
        ParameterTool params = AbcFlinkArgs.assembleArgs(args);
        // 添加启动日志
        log.info("starting bin log sync job with params: {}", params.toMap());

        String region = params.get(Constant.Params.REGION);
        String env = params.get(Constant.Params.ENV);
        String mode = params.get(Constant.Params.MODE);
        String hotInstanceName = params.get(Constant.Params.HOT_INSTANCE_NAME);
        CommonUtils.validParams(region, env, mode);

        // 加载应用配置
        AppConfig appConfig = PropertiesLoader.load(region, env, hotInstanceName);
        if (appConfig == null) {
            throw new RuntimeException("Failed to load application configuration");
        }

        // 构建执行环境
        StreamExecutionEnvironment environment = AbcFlinkEnvironment.build(params);
        configureFlinkJob(environment, appConfig);
        Integer[] reBalanceKeys = createReBalanceKeys(environment.getParallelism());

        // 创建 MySQL CDC 源
        MySqlSource<ChangeEvent> mySqlCdcSource = createMySqlCdcSource(appConfig.getSourceDb());

        // 添加源到执行环境
        DataStream<ChangeEvent> sourceStream = environment
                .fromSource(mySqlCdcSource, WatermarkStrategy.forMonotonousTimestamps(), "MySQL-CDC-Source")
                .setParallelism(1)
                .uid("mysql-cdc-source")
                .disableChaining();
        // 添加 sink 写入目标 MySQL
        sourceStream.assignTimestampsAndWatermarks(WatermarkStrategy.<ChangeEvent>forGenerator(ctx -> new GtidAwareWatermarkGenerator(org.apache.flink.streaming.api.windowing.time.Time.seconds(0L)))
                        .withTimestampAssigner((event, ts) -> event.getTimestamp()))
                .keyBy(new PrimaryKeySelector(reBalanceKeys))

                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.milliseconds(10)))
                .allowedLateness(org.apache.flink.streaming.api.windowing.time.Time.seconds(0))

                .process(new CombineWindowFunction())
//                .addSink(new MySQLSinkFunction(appConfig.getTargetDb()))
                .name("MySQL-Sink")
                .uid("mysql-sink");

        // 执行任务
        environment.execute(CommonUtils.generateJobName(appConfig.getName(), params));
    }


    /**
     * 配置 Flink 作业参数
     */
    private static void configureFlinkJob(StreamExecutionEnvironment env, AppConfig config) {
        // 设置并行度
//        env.setParallelism(config.getParallelism());

        // 配置检查点以实现精确一次处理
        if (config.isEnableExactlyOnce()) {
            env.enableCheckpointing(config.getCheckpointInterval(), CheckpointingMode.EXACTLY_ONCE);
            env.getCheckpointConfig().setMinPauseBetweenCheckpoints(config.getCheckpointInterval() / 2);
            env.getCheckpointConfig().setCheckpointTimeout(config.getCheckpointInterval() * 2L);
            env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
            env.getCheckpointConfig().setExternalizedCheckpointCleanup(
                    CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        }
        env.setStreamTimeCharacteristic(TimeCharacteristic.EventTime);
        // 配置重启策略
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(
                3, // 重启尝试次数
                Time.of(10, TimeUnit.SECONDS) // 重启之间的延迟
        ));
    }

    /**
     * 创建 MySQL CDC 源函数
     */
    private static MySqlSource<ChangeEvent> createMySqlCdcSource(MySQLConfig config) {
        Properties debeziumProperties = new Properties();
        debeziumProperties.setProperty("database.serverTimezone", config.getServerTimeZone());
        debeziumProperties.setProperty("converters", "datetimeConverter");
        debeziumProperties.setProperty("datetimeConverter.type", "cn.abcyun.cis.dts.util.MysqlDateTimeConverter");

        return MySqlSource.<ChangeEvent>builder()
                .hostname(config.getHost())
                .port(config.getPort())
                .databaseList(buildDbListPattern(config))
                .tableList(buildTableListPattern(config))
                .username(config.getUsername())
                .password(config.getPassword())
                .startupOptions(StartupOptions.latest())
//                .startupOptions(StartupOptions.timestamp(1761006580000L))
                .debeziumProperties(debeziumProperties)
                .deserializer(new MySqlCdcEventDeserializer())
                .splitSize(512)
                .build();
    }

    /**
     * 构建库列表模式
     */
    private static String buildDbListPattern(MySQLConfig config) {
        StringBuilder pattern = new StringBuilder();
        for (int i = 0; i < config.getTableNames().length; i++) {
            if (i > 0) {
                pattern.append(",");
            }
            pattern.append(config.getTableNames()[i].split("\\.")[0]);
        }
        return pattern.toString();
    }

    /**
     * 构建表列表模式
     */
    private static String buildTableListPattern(MySQLConfig config) {
        // 如果指定了表，则构建表列表模式
        StringBuilder pattern = new StringBuilder();
        for (int i = 0; i < config.getTableNames().length; i++) {
            if (i > 0) {
                pattern.append(",");
            }
            pattern.append(config.getTableNames()[i]);
        }
        log.warn(pattern.toString());
        return pattern.toString();
    }

    private static Integer[] createReBalanceKeys(int parallelism) {
        int maxParallelism = KeyGroupRangeAssignment.computeDefaultMaxParallelism(parallelism);
        int maxRandomKey = parallelism * 12;
        Map<Integer, Integer> keySubIndexMap = new HashMap<>();
        for (int randomKey = 0; randomKey < maxRandomKey; randomKey++) {
            int subtaskIndex = KeyGroupRangeAssignment.assignKeyToParallelOperator(randomKey, maxParallelism, parallelism);
            if (keySubIndexMap.containsKey(subtaskIndex)) {
                continue;
            }
            keySubIndexMap.put(subtaskIndex, randomKey);
        }
        return keySubIndexMap.values().toArray(new Integer[keySubIndexMap.size()]);
    }
}
