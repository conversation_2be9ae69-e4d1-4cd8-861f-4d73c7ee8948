package cn.abcyun.cis.dts;

import cn.abcyun.cis.dts.config.AppConfig;
import cn.abcyun.cis.dts.config.MySQLConfig;
import cn.abcyun.cis.dts.operator.DeleteEventFilter;
import cn.abcyun.cis.dts.operator.MySQLSinkFunction;
import cn.abcyun.cis.dts.util.CommonUtils;
import cn.abcyun.cis.dts.util.Constant;
import cn.abcyun.cis.dts.util.PropertiesLoader;
import cn.abcyun.cis.dts.model.ChangeEvent;
import cn.abcyun.cis.dts.operator.MySqlCdcEventDeserializer;
import cn.abcyun.commons.flink.common.*;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

@Slf4j
public class Hot2ColdBusinessSyncJob {
    public static void main(String[] args) throws Exception {
        ParameterTool params = AbcFlinkArgs.assembleArgs(args);
        // 添加启动日志
        log.info("starting bin log sync job with params: {}", params.toMap());

        String region = params.get(Constant.Params.REGION);
        String env = params.get(Constant.Params.ENV);
        String mode = params.get(Constant.Params.MODE);
        String hotInstanceName = params.get(Constant.Params.HOT_INSTANCE_NAME);
        CommonUtils.validParams(region, env, mode);

        // 加载应用配置
        AppConfig appConfig = PropertiesLoader.load(region, env, hotInstanceName);
        if (appConfig == null) {
            throw new RuntimeException("Failed to load application configuration");
        }

        // 构建执行环境
        StreamExecutionEnvironment environment = AbcFlinkEnvironment.build(params);
        configureFlinkJob(environment, appConfig);

        // 创建 MySQL CDC 源
        MySqlSource<ChangeEvent> mySqlCdcSource = createMySqlCdcSource(appConfig.getSourceDb());

        // 添加源到执行环境
        DataStream<ChangeEvent> sourceStream = environment
                .fromSource(mySqlCdcSource, WatermarkStrategy.forMonotonousTimestamps(), "MySQL-CDC-Source")
                .setParallelism(1)
                .uid("mysql-cdc-source")
                .disableChaining();
        // 应用 DELETE 事件过滤器
        DataStream<ChangeEvent> filteredStream = sourceStream
                .filter(new DeleteEventFilter())
                .name("Delete-Event-Filter")
                .uid("delete-event-filter")
                .disableChaining();
        // 添加 sink 写入目标 MySQL
        filteredStream.addSink(new MySQLSinkFunction(appConfig.getTargetDb()))
                .name("MySQL-Sink")
                .uid("mysql-sink");

        // 执行任务
        environment.execute(CommonUtils.generateJobName(appConfig.getName(), params));
    }


    /**
     * 配置 Flink 作业参数
     */
    private static void configureFlinkJob(StreamExecutionEnvironment env, AppConfig config) {
        // 设置并行度
//        env.setParallelism(config.getParallelism());

        // 配置检查点以实现精确一次处理
        if (config.isEnableExactlyOnce()) {
            env.enableCheckpointing(config.getCheckpointInterval(), CheckpointingMode.EXACTLY_ONCE);
            env.getCheckpointConfig().setMinPauseBetweenCheckpoints(config.getCheckpointInterval() / 2);
            env.getCheckpointConfig().setCheckpointTimeout(config.getCheckpointInterval() * 2L);
            env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
            env.getCheckpointConfig().setExternalizedCheckpointCleanup(
                    CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        }

        // 配置重启策略
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(
                3, // 重启尝试次数
                Time.of(10, TimeUnit.SECONDS) // 重启之间的延迟
        ));
    }

    /**
     * 创建 MySQL CDC 源函数
     */
    private static MySqlSource<ChangeEvent> createMySqlCdcSource(MySQLConfig config) {
        Properties debeziumProperties = new Properties();
        debeziumProperties.setProperty("database.serverTimezone", config.getServerTimeZone());
        debeziumProperties.setProperty("converters", "datetimeConverter");
        debeziumProperties.setProperty("datetimeConverter.type", "cn.abcyun.cis.dts.util.MysqlDateTimeConverter");

        return MySqlSource.<ChangeEvent>builder()
                .hostname(config.getHost())
                .port(config.getPort())
                .databaseList(buildDbListPattern(config))
                .tableList(buildTableListPattern(config))
                .username(config.getUsername())
                .password(config.getPassword())
                .startupOptions(StartupOptions.latest())
                .debeziumProperties(debeziumProperties)
                .deserializer(new MySqlCdcEventDeserializer())
                .splitSize(512)
                .build();
    }

    /**
     * 构建库列表模式
     */
    private static String buildDbListPattern(MySQLConfig config) {
        StringBuilder pattern = new StringBuilder();
        for (int i = 0; i < config.getTableNames().length; i++) {
            if (i > 0) {
                pattern.append(",");
            }
            pattern.append(config.getTableNames()[i].split("\\.")[0]);
        }
        return pattern.toString();
    }

    /**
     * 构建表列表模式
     */
    private static String buildTableListPattern(MySQLConfig config) {
        // 如果指定了表，则构建表列表模式
        StringBuilder pattern = new StringBuilder();
        for (int i = 0; i < config.getTableNames().length; i++) {
            if (i > 0) {
                pattern.append(",");
            }
            pattern.append(config.getTableNames()[i]);
        }
        log.warn(pattern.toString());
        return pattern.toString();
    }
}
