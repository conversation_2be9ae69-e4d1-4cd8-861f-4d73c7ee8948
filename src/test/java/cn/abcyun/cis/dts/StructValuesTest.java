package cn.abcyun.cis.dts;

import cn.abcyun.cis.dts.operator.MySqlCdcEventDeserializer;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Field;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Schema;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.SchemaBuilder;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Struct;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 convertStruct 方法基于 Struct.values 的字段存在性判断
 */
public class StructValuesTest {

    @Test
    void testConvertStructWithMissingFieldsInValues() throws Exception {
        System.out.println("=== Testing Struct with fields in schema but missing in values ===");
        
        // 创建包含更多字段的 Schema（模拟您提到的情况）
        Schema schema = SchemaBuilder.struct()
                .field("id", Schema.STRING_SCHEMA)
                .field("patient_order_id", Schema.STRING_SCHEMA)
                .field("consulting_room_id", Schema.OPTIONAL_STRING_SCHEMA) // 这个字段在 schema 中但不在 values 中
                .field("department_name", Schema.OPTIONAL_STRING_SCHEMA)
                .field("doctor_name", Schema.OPTIONAL_STRING_SCHEMA)
                .field("status", Schema.OPTIONAL_INT32_SCHEMA)
                .field("fee", Schema.OPTIONAL_FLOAT64_SCHEMA)
                .field("created", Schema.OPTIONAL_STRING_SCHEMA)
                .build();

        // 创建 Struct，但不设置 consulting_room_id 字段
        Struct struct = new Struct(schema);
        struct.put("id", "ffffffff0000000035196bd123814002");
        struct.put("patient_order_id", "ffffffff0000000035196bd121410000");
        // 注意：consulting_room_id 字段没有被设置
        struct.put("department_name", "内科");
        struct.put("doctor_name", "姜新魁");
        struct.put("status", 1);
        struct.put("fee", 0.0000);
        struct.put("created", "2025-11-03 21:26:33");

        System.out.println("Original Struct: " + struct.toString());
        System.out.println("Schema fields count: " + schema.fields().size());

        // 测试 getAvailableFieldNames 方法
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method getAvailableFieldsMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("getAvailableFieldNames", Struct.class);
        getAvailableFieldsMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Set<String> availableFields = (Set<String>) getAvailableFieldsMethod.invoke(deserializer, struct);
        
        System.out.println("Available fields in Struct values: " + availableFields);

        // 验证 consulting_room_id 不在可用字段中
        assertFalse(availableFields.contains("consulting_room_id"), 
            "consulting_room_id should not be in available fields");
        
        // 验证其他字段在可用字段中
        assertTrue(availableFields.contains("id"));
        assertTrue(availableFields.contains("patient_order_id"));
        assertTrue(availableFields.contains("department_name"));
        assertTrue(availableFields.contains("doctor_name"));
        assertTrue(availableFields.contains("status"));
        assertTrue(availableFields.contains("fee"));
        assertTrue(availableFields.contains("created"));

        // 测试 convertStruct 方法
        Method convertMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("convertStruct", Struct.class);
        convertMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) convertMethod.invoke(deserializer, struct);

        System.out.println("Converted Map: " + result);
        System.out.println("Map size: " + result.size());

        // 验证结果
        assertNotNull(result);
        assertEquals(7, result.size(), "Map should contain 7 fields (excluding consulting_room_id)");

        // 验证存在的字段
        assertTrue(result.containsKey("id"));
        assertTrue(result.containsKey("patient_order_id"));
        assertTrue(result.containsKey("department_name"));
        assertTrue(result.containsKey("doctor_name"));
        assertTrue(result.containsKey("status"));
        assertTrue(result.containsKey("fee"));
        assertTrue(result.containsKey("created"));

        // 验证不存在的字段
        assertFalse(result.containsKey("consulting_room_id"), 
            "consulting_room_id should not be in the result map");

        // 验证值的正确性
        assertEquals("ffffffff0000000035196bd123814002", result.get("id"));
        assertEquals("内科", result.get("department_name"));
        assertEquals("姜新魁", result.get("doctor_name"));
        assertEquals(1, result.get("status"));
        assertEquals(0.0000, result.get("fee"));

        System.out.println("✓ Test passed: Fields missing in Struct.values are correctly excluded");
    }

    @Test
    void testParseStructToString() throws Exception {
        System.out.println("=== Testing parseStructToString method ===");
        
        // 创建一个模拟您提供的数据的 Struct
        Schema schema = SchemaBuilder.struct()
                .field("id", Schema.STRING_SCHEMA)
                .field("patient_order_id", Schema.STRING_SCHEMA)
                .field("department_name", Schema.STRING_SCHEMA)
                .field("doctor_name", Schema.STRING_SCHEMA)
                .field("fee", Schema.FLOAT64_SCHEMA)
                .field("status", Schema.INT32_SCHEMA)
                .field("created", Schema.STRING_SCHEMA)
                .build();

        Struct struct = new Struct(schema);
        struct.put("id", "ffffffff0000000035196bd123814002");
        struct.put("patient_order_id", "ffffffff0000000035196bd121410000");
        struct.put("department_name", "内科");
        struct.put("doctor_name", "姜新魁");
        struct.put("fee", 0.0000);
        struct.put("status", 1);
        struct.put("created", "2025-11-03 21:26:33");

        System.out.println("Struct toString: " + struct.toString());

        // 测试 parseStructToString 方法
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method parseMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("parseStructToString", Struct.class);
        parseMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Set<String> parsedFields = (Set<String>) parseMethod.invoke(deserializer, struct);

        System.out.println("Parsed fields: " + parsedFields);

        // 验证解析结果
        assertEquals(7, parsedFields.size());
        assertTrue(parsedFields.contains("id"));
        assertTrue(parsedFields.contains("patient_order_id"));
        assertTrue(parsedFields.contains("department_name"));
        assertTrue(parsedFields.contains("doctor_name"));
        assertTrue(parsedFields.contains("fee"));
        assertTrue(parsedFields.contains("status"));
        assertTrue(parsedFields.contains("created"));

        System.out.println("✓ Test passed: parseStructToString correctly extracts field names");
    }

    @Test
    void testConvertStructWithNullValuesButExistingFields() throws Exception {
        System.out.println("=== Testing Struct with null values but existing fields ===");
        
        // 创建 Schema
        Schema schema = SchemaBuilder.struct()
                .field("id", Schema.STRING_SCHEMA)
                .field("name", Schema.OPTIONAL_STRING_SCHEMA)
                .field("amount", Schema.OPTIONAL_FLOAT64_SCHEMA)
                .field("status", Schema.OPTIONAL_STRING_SCHEMA)
                .build();

        // 创建 Struct，其中一些字段设置为 null（但字段存在）
        Struct struct = new Struct(schema);
        struct.put("id", "test123");
        struct.put("name", null); // 字段存在但值为 null
        struct.put("amount", 100.50);
        struct.put("status", null); // 字段存在但值为 null

        System.out.println("Struct with null values: " + struct.toString());

        // 测试转换
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method convertMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("convertStruct", Struct.class);
        convertMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) convertMethod.invoke(deserializer, struct);

        System.out.println("Converted Map: " + result);

        // 验证结果：即使值为 null，但字段在 Struct.values 中存在，所以应该被包含
        assertEquals(4, result.size(), "All 4 fields should be included even if some are null");
        assertTrue(result.containsKey("id"));
        assertTrue(result.containsKey("name"));
        assertTrue(result.containsKey("amount"));
        assertTrue(result.containsKey("status"));

        // 验证值
        assertEquals("test123", result.get("id"));
        assertNull(result.get("name"));
        assertEquals(100.50, result.get("amount"));
        assertNull(result.get("status"));

        System.out.println("✓ Test passed: Null values are included when fields exist in Struct.values");
    }

    @Test
    void testRealWorldExample() throws Exception {
        System.out.println("=== Testing real-world example scenario ===");
        
        // 模拟您提供的真实数据场景
        Schema schema = SchemaBuilder.struct()
                .field("id", Schema.STRING_SCHEMA)
                .field("patient_order_id", Schema.STRING_SCHEMA)
                .field("registration_sheet_id", Schema.STRING_SCHEMA)
                .field("consulting_room_id", Schema.OPTIONAL_STRING_SCHEMA) // 在 schema 中但不在 values 中
                .field("department_name", Schema.STRING_SCHEMA)
                .field("doctor_name", Schema.STRING_SCHEMA)
                .field("fee", Schema.FLOAT64_SCHEMA)
                .field("status", Schema.INT32_SCHEMA)
                .field("created", Schema.STRING_SCHEMA)
                .build();

        // 创建 Struct，不设置 consulting_room_id
        Struct struct = new Struct(schema);
        struct.put("id", "ffffffff0000000035196bd123814002");
        struct.put("patient_order_id", "ffffffff0000000035196bd121410000");
        struct.put("registration_sheet_id", "ffffffff0000000035196bd123814000");
        // consulting_room_id 不设置
        struct.put("department_name", "内科");
        struct.put("doctor_name", "姜新魁");
        struct.put("fee", 0.0000);
        struct.put("status", 1);
        struct.put("created", "2025-11-03 21:26:33");

        System.out.println("Real-world Struct: " + struct.toString());
        System.out.println("Schema defines {} fields", schema.fields().size());

        // 转换
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method convertMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("convertStruct", Struct.class);
        convertMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) convertMethod.invoke(deserializer, struct);

        System.out.println("Result Map: " + result);
        System.out.println("Schema fields: {} -> Map fields: {}", schema.fields().size(), result.size());

        // 验证：应该只包含实际存在于 Struct.values 中的字段
        assertEquals(8, result.size(), "Should contain 8 fields (excluding consulting_room_id)");
        assertFalse(result.containsKey("consulting_room_id"), 
            "consulting_room_id should be excluded because it's not in Struct.values");

        // 验证包含的字段
        String[] expectedFields = {
            "id", "patient_order_id", "registration_sheet_id", 
            "department_name", "doctor_name", "fee", "status", "created"
        };
        
        for (String field : expectedFields) {
            assertTrue(result.containsKey(field), "Field " + field + " should be included");
        }

        System.out.println("✓ Test passed: Real-world scenario handled correctly");
    }
}
