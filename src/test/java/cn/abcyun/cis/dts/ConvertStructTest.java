package cn.abcyun.cis.dts;

import cn.abcyun.cis.dts.operator.MySqlCdcEventDeserializer;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Field;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Schema;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.SchemaBuilder;
import com.ververica.cdc.connectors.shaded.org.apache.kafka.connect.data.Struct;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 convertStruct 方法的空值处理
 */
public class ConvertStructTest {

    @Test
    void testConvertStructWithNullValues() throws Exception {
        // 创建测试用的 Schema
        Schema schema = SchemaBuilder.struct()
                .field("id", Schema.INT64_SCHEMA)
                .field("name", Schema.OPTIONAL_STRING_SCHEMA)
                .field("amount", Schema.OPTIONAL_FLOAT64_SCHEMA)
                .field("status", Schema.OPTIONAL_STRING_SCHEMA)
                .field("created_time", Schema.OPTIONAL_STRING_SCHEMA)
                .build();

        // 创建 Struct，其中一些字段为 null
        Struct struct = new Struct(schema);
        struct.put("id", 123L);
        struct.put("name", "test_name");
        struct.put("amount", null); // null 值
        struct.put("status", "ACTIVE");
        struct.put("created_time", null); // null 值

        // 使用反射调用 convertStruct 方法
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method convertMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("convertStruct", Struct.class);
        convertMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) convertMethod.invoke(deserializer, struct);

        // 验证结果
        assertNotNull(result);
        
        // 验证非 null 字段被包含
        assertTrue(result.containsKey("id"));
        assertTrue(result.containsKey("name"));
        assertTrue(result.containsKey("status"));
        assertEquals(123L, result.get("id"));
        assertEquals("test_name", result.get("name"));
        assertEquals("ACTIVE", result.get("status"));

        // 验证 null 字段不被包含
        assertFalse(result.containsKey("amount"), "Null field 'amount' should not be included in the map");
        assertFalse(result.containsKey("created_time"), "Null field 'created_time' should not be included in the map");

        // 验证 Map 的大小
        assertEquals(3, result.size(), "Map should only contain 3 non-null fields");

        System.out.println("✓ convertStruct with null values test passed");
        System.out.println("Original Struct fields: " + schema.fields().size());
        System.out.println("Converted Map fields: " + result.size());
        System.out.println("Map contents: " + result);
    }

    @Test
    void testConvertStructWithAllNullValues() throws Exception {
        // 创建测试用的 Schema
        Schema schema = SchemaBuilder.struct()
                .field("field1", Schema.OPTIONAL_STRING_SCHEMA)
                .field("field2", Schema.OPTIONAL_INT32_SCHEMA)
                .field("field3", Schema.OPTIONAL_FLOAT64_SCHEMA)
                .build();

        // 创建 Struct，所有字段都为 null
        Struct struct = new Struct(schema);
        struct.put("field1", null);
        struct.put("field2", null);
        struct.put("field3", null);

        // 使用反射调用 convertStruct 方法
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method convertMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("convertStruct", Struct.class);
        convertMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) convertMethod.invoke(deserializer, struct);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty(), "Map should be empty when all fields are null");

        System.out.println("✓ convertStruct with all null values test passed");
        System.out.println("Original Struct fields: " + schema.fields().size());
        System.out.println("Converted Map fields: " + result.size());
    }

    @Test
    void testConvertStructWithAllNonNullValues() throws Exception {
        // 创建测试用的 Schema
        Schema schema = SchemaBuilder.struct()
                .field("id", Schema.INT64_SCHEMA)
                .field("name", Schema.STRING_SCHEMA)
                .field("amount", Schema.FLOAT64_SCHEMA)
                .field("active", Schema.BOOLEAN_SCHEMA)
                .build();

        // 创建 Struct，所有字段都有值
        Struct struct = new Struct(schema);
        struct.put("id", 456L);
        struct.put("name", "test_user");
        struct.put("amount", 99.99);
        struct.put("active", true);

        // 使用反射调用 convertStruct 方法
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method convertMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("convertStruct", Struct.class);
        convertMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) convertMethod.invoke(deserializer, struct);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.size(), "Map should contain all 4 fields");

        // 验证所有字段都被包含
        assertTrue(result.containsKey("id"));
        assertTrue(result.containsKey("name"));
        assertTrue(result.containsKey("amount"));
        assertTrue(result.containsKey("active"));

        // 验证值的正确性
        assertEquals(456L, result.get("id"));
        assertEquals("test_user", result.get("name"));
        assertEquals(99.99, result.get("amount"));
        assertEquals(true, result.get("active"));

        System.out.println("✓ convertStruct with all non-null values test passed");
        System.out.println("All fields preserved: " + result);
    }

    @Test
    void testConvertStructWithNullStruct() throws Exception {
        // 测试传入 null Struct 的情况
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method convertMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("convertStruct", Struct.class);
        convertMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) convertMethod.invoke(deserializer, (Struct) null);

        // 验证结果
        assertNull(result, "Result should be null when input Struct is null");

        System.out.println("✓ convertStruct with null Struct test passed");
    }

    @Test
    void testConvertStructWithMixedDataTypes() throws Exception {
        // 测试包含各种数据类型的 Struct
        Schema schema = SchemaBuilder.struct()
                .field("long_field", Schema.OPTIONAL_INT64_SCHEMA)
                .field("int_field", Schema.OPTIONAL_INT32_SCHEMA)
                .field("double_field", Schema.OPTIONAL_FLOAT64_SCHEMA)
                .field("string_field", Schema.OPTIONAL_STRING_SCHEMA)
                .field("boolean_field", Schema.OPTIONAL_BOOLEAN_SCHEMA)
                .field("null_field", Schema.OPTIONAL_STRING_SCHEMA)
                .build();

        Struct struct = new Struct(schema);
        struct.put("long_field", 123456789L);
        struct.put("int_field", 42);
        struct.put("double_field", 11827926.92); // 精度敏感的数值
        struct.put("string_field", "{\"price\": 11827926.92}"); // JSON 字符串
        struct.put("boolean_field", false);
        struct.put("null_field", null); // null 值

        // 使用反射调用 convertStruct 方法
        MySqlCdcEventDeserializer deserializer = new MySqlCdcEventDeserializer();
        Method convertMethod = MySqlCdcEventDeserializer.class.getDeclaredMethod("convertStruct", Struct.class);
        convertMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) convertMethod.invoke(deserializer, struct);

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.size(), "Map should contain 5 non-null fields");

        // 验证各种数据类型
        assertEquals(123456789L, result.get("long_field"));
        assertEquals(42, result.get("int_field"));
        assertEquals(11827926.92, result.get("double_field"));
        assertEquals("{\"price\": 11827926.92}", result.get("string_field"));
        assertEquals(false, result.get("boolean_field"));

        // 验证 null 字段不被包含
        assertFalse(result.containsKey("null_field"));

        System.out.println("✓ convertStruct with mixed data types test passed");
        System.out.println("Mixed data types preserved correctly:");
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            System.out.println("  {}: {} ({})", 
                entry.getKey(), entry.getValue(), entry.getValue().getClass().getSimpleName());
        }
    }
}
